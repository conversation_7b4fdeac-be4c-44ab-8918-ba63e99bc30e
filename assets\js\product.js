/**
 * Product page JavaScript for DmrThema
 */

jQuery(document).ready(function($) {
    
    // Product gallery lightbox
    if (typeof $.fn.magnificPopup !== 'undefined') {
        $('.woocommerce-product-gallery__image a').magnificPopup({
            type: 'image',
            gallery: {
                enabled: true
            }
        });
    }
    
    // Product tabs
    $('.woocommerce-tabs .tabs li a').on('click', function(e) {
        e.preventDefault();
        
        var target = $(this).attr('href');
        
        // Remove active class from all tabs and panels
        $('.woocommerce-tabs .tabs li').removeClass('active');
        $('.woocommerce-tabs .panel').hide();
        
        // Add active class to clicked tab and show corresponding panel
        $(this).parent().addClass('active');
        $(target).show();
    });
    
    // Quantity buttons
    $(document).on('click', '.quantity .plus', function() {
        var $input = $(this).siblings('.qty');
        var val = parseInt($input.val()) || 0;
        var max = parseInt($input.attr('max')) || 999;
        
        if (val < max) {
            $input.val(val + 1).trigger('change');
        }
    });
    
    $(document).on('click', '.quantity .minus', function() {
        var $input = $(this).siblings('.qty');
        var val = parseInt($input.val()) || 0;
        var min = parseInt($input.attr('min')) || 1;
        
        if (val > min) {
            $input.val(val - 1).trigger('change');
        }
    });
    
    // Product variations
    $('.variations select').on('change', function() {
        var $form = $(this).closest('form.variations_form');
        $form.trigger('woocommerce_variation_select_change');
    });

    // Simdi Al butonu ekle
    function addBuyNowButton() {
        $('.single_add_to_cart_button').each(function() {
            var $addToCartBtn = $(this);
            var $form = $addToCartBtn.closest('form.cart');

            // Eger zaten Simdi Al butonu varsa, ekleme
            if ($form.find('.buy_now_button').length > 0) {
                return;
            }

            // Simdi Al butonunu olustur
            var $buyNowBtn = $('<button type="button" class="buy_now_button">Şimdi Al</button>');

            // Butonu sepete ekle butonundan sonra ekle
            $addToCartBtn.after($buyNowBtn);

            // Simdi Al butonu click eventi
            $buyNowBtn.on('click', function(e) {
                e.preventDefault();

                // Form validasyonu - varyasyonlu urunler icin
                if ($form.hasClass('variations_form')) {
                    var variationId = $form.find('input[name="variation_id"]').val();
                    if (!variationId || variationId === '0') {
                        // Varyasyon secilmemis, hata mesaji goster
                        alert('Lütfen ürün seçeneklerini belirleyin.');
                        return;
                    }
                }

                $buyNowBtn.text('İşleniyor...');

                // Urun bilgilerini topla
                var productId = $form.find('[name="add-to-cart"]').val();
                var quantity = $form.find('[name="quantity"]').val() || 1;
                var variationId = $form.find('[name="variation_id"]').val() || 0;

                // Variation attributes'lari topla
                var variationData = {};
                $form.find('[name^="attribute_"]').each(function() {
                    if ($(this).val()) {
                        variationData[$(this).attr('name')] = $(this).val();
                    }
                });

                // AJAX ile sepeti temizle ve urunu ekle, sonra odeme sayfasina yonlendir
                $.ajax({
                    type: 'POST',
                    url: (typeof dmrthema_wc_params !== 'undefined' && dmrthema_wc_params.ajax_url)
                        ? dmrthema_wc_params.ajax_url
                        : '/wp-admin/admin-ajax.php',
                    data: {
                        action: 'dmrthema_buy_now_direct',
                        product_id: productId,
                        quantity: quantity,
                        variation_id: variationId,
                        variation_data: variationData,
                        security: 'buy_now_nonce'
                    },
                    success: function(response) {
                        if (response.success && response.data.checkout_url) {
                            // Basarili, odeme sayfasina yonlendir
                            window.location.href = response.data.checkout_url;
                        } else {
                            // Hata durumu
                            console.log('Buy now failed:', response.data);
                            alert('Bir hata oluştu. Lütfen tekrar deneyin.');
                            $buyNowBtn.text('Şimdi Al');
                        }
                    },
                    error: function() {
                        console.log('Buy now AJAX failed');
                        alert('Bir hata oluştu. Lütfen tekrar deneyin.');
                        $buyNowBtn.text('Şimdi Al');
                    }
                });
            });
        });
    }

    // Sayfa yuklendiginde butonu ekle
    addBuyNowButton();

    // AJAX ile icerik guncellendiginde tekrar ekle
    $(document.body).on('updated_wc_div', function() {
        addBuyNowButton();
    });

    // Add to cart AJAX
    $(document).on('click', '.single_add_to_cart_button:not(.disabled)', function(e) {
        var $button = $(this);
        var $form = $button.closest('form.cart');
        
        if ($form.length === 0) {
            return;
        }
        
        // Check if product is variable and variation is selected
        if ($form.hasClass('variations_form')) {
            var variationId = $form.find('input[name="variation_id"]').val();
            if (!variationId || variationId === '0') {
                return; // Let default behavior handle the error
            }
        }
        
        e.preventDefault();
        
        $button.addClass('loading');
        
        var formData = $form.serialize();
        formData += '&add-to-cart=' + $form.find('[name="add-to-cart"]').val();
        
        $.ajax({
            type: 'POST',
            url: wc_add_to_cart_params.wc_ajax_url.toString().replace('%%endpoint%%', 'add_to_cart'),
            data: formData,
            success: function(response) {
                if (response.error && response.product_url) {
                    window.location = response.product_url;
                    return;
                }
                
                // Trigger cart update
                $(document.body).trigger('added_to_cart', [response.fragments, response.cart_hash, $button]);
                
                // Show success message
                if (response.fragments) {
                    // Update cart fragments
                    $.each(response.fragments, function(key, value) {
                        $(key).replaceWith(value);
                    });
                }
            },
            error: function() {
                // Handle error
                console.log('Add to cart failed');
            },
            complete: function() {
                $button.removeClass('loading');
            }
        });
    });
    
    // Product image zoom
    if (typeof $.fn.zoom !== 'undefined') {
        $('.woocommerce-product-gallery__image').zoom();
    }
    
    // Product gallery slider
    if ($('.woocommerce-product-gallery').length > 0) {
        $('.woocommerce-product-gallery').flexslider({
            animation: "slide",
            controlNav: "thumbnails",
            animationLoop: false,
            slideshow: false,
            itemWidth: 400,
            itemMargin: 5,
            asNavFor: '.woocommerce-product-gallery'
        });
    }
    
    // Reviews toggle
    $('.woocommerce-review-link').on('click', function(e) {
        e.preventDefault();
        $('.woocommerce-tabs .tabs li.reviews_tab a').trigger('click');
        $('html, body').animate({
            scrollTop: $('.woocommerce-tabs').offset().top - 100
        }, 500);
    });
    
    // Stock status updates
    $(document).on('found_variation', function(event, variation) {
        if (variation.is_in_stock) {
            $('.stock').removeClass('out-of-stock').addClass('in-stock');
        } else {
            $('.stock').removeClass('in-stock').addClass('out-of-stock');
        }
    });
    
    // Reset variations
    $(document).on('reset_data', function() {
        $('.stock').removeClass('out-of-stock in-stock');
    });
    
});
